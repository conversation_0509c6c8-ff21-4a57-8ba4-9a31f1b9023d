"""
Enhanced cache management module for bus route application.
Provides optimized in-memory caching with Redis fallback for performance optimization.
Updated with improvements from Vercel version while maintaining Redis compatibility.
"""

import json
import time
import hashlib
from typing import Optional, Any, Dict
from datetime import datetime, timedelta
import logging

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from cachetools import TTLCache

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CacheManager:
    """
    Enhanced cache manager with optimized in-memory caching and Redis fallback.
    Uses separate TTL caches for different data types for better performance.
    """

    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        self.redis_client = None

        # Use separate caches for different data types with appropriate TTL
        # Routes and routestops are very static (change ~once per year)
        self.caches = {
            'routes': TTLCache(maxsize=200, ttl=30 * 24 * 3600),     # 30 days - routes rarely change
            'routestops': TTLCache(maxsize=200, ttl=30 * 24 * 3600), # 30 days - route-stop relationships rarely change
            'stops': TTLCache(maxsize=500, ttl=90 * 24 * 3600),      # 90 days - stop info is extremely static
            'eta': TTLCache(maxsize=100, ttl=30),                    # 30 seconds - REAL-TIME!
            'default': TTLCache(maxsize=100, ttl=3600)               # 1 hour default
        }

        # Cache TTL settings (for reference)
        self.cache_ttl = {
            'routes': 30 * 24 * 3600,     # 30 days - routes change very rarely
            'routestops': 30 * 24 * 3600, # 30 days - route-stop relationships change very rarely
            'stops': 90 * 24 * 3600,      # 90 days - stop info is extremely static
            'eta': 30,                    # 30 seconds - ETA data is real-time
            'default': 3600               # 1 hour default
        }

        # Initialize Redis connection (fallback)
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.from_url(redis_url, decode_responses=True)
                # Test connection
                self.redis_client.ping()
                logger.info("Redis cache initialized successfully as fallback")
            except Exception as e:
                logger.warning(f"Redis connection failed: {e}. Using memory cache only.")
                self.redis_client = None
        else:
            logger.warning("Redis not available. Using memory cache only.")

        logger.info("Enhanced in-memory cache initialized with optimized TTL handling")
        logger.info(f"Cache TTL settings:")
        logger.info(f"  - Routes: {self.cache_ttl['routes'] // (24*3600)} days (very static)")
        logger.info(f"  - Route-stops: {self.cache_ttl['routestops'] // (24*3600)} days (very static)")
        logger.info(f"  - Stops: {self.cache_ttl['stops'] // (24*3600)} days (extremely static)")
        logger.info(f"  - ETA: {self.cache_ttl['eta']} seconds (real-time)")
    
    def _generate_key(self, prefix: str, *args) -> str:
        """Generate a consistent cache key from prefix and arguments."""
        key_data = f"{prefix}:{':'.join(str(arg) for arg in args)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str, data_type: str = 'default') -> Optional[Any]:
        """Get value from appropriate cache based on data type."""
        try:
            # Try appropriate memory cache first (faster)
            cache = self.caches.get(data_type, self.caches['default'])
            result = cache.get(key)
            if result is not None:
                if data_type == 'eta':
                    logger.debug(f"ETA cache hit for key: {key}")
                return result

            # Fallback to Redis if available
            if self.redis_client:
                value = self.redis_client.get(key)
                if value:
                    parsed_value = json.loads(value)
                    # Store in memory cache for faster subsequent access
                    cache[key] = parsed_value
                    return parsed_value

            return None

        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None

    def set(self, key: str, value: Any, data_type: str = 'default') -> bool:
        """Set value in appropriate cache with correct TTL."""
        try:
            # Set in appropriate memory cache
            cache = self.caches.get(data_type, self.caches['default'])
            cache[key] = value
            if data_type == 'eta':
                logger.debug(f"ETA data cached for {self.cache_ttl['eta']} seconds: {key}")

            # Also set in Redis as backup (if available)
            if self.redis_client:
                ttl = self.cache_ttl.get(data_type, self.cache_ttl['default'])
                self.redis_client.setex(key, ttl, json.dumps(value))

            return True

        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from all caches."""
        try:
            deleted = False
            # Delete from all memory caches
            for cache_name, cache in self.caches.items():
                if key in cache:
                    del cache[key]
                    deleted = True

            # Delete from Redis if available
            if self.redis_client:
                self.redis_client.delete(key)
                deleted = True

            return deleted

        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False

    def clear_by_pattern(self, pattern: str) -> int:
        """Clear cache entries matching pattern (Redis only)."""
        if not self.redis_client:
            return 0

        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Cache clear pattern error: {e}")
            return 0

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for all cache types."""
        stats = {
            'redis_available': self.redis_client is not None,
            'cache_type': 'enhanced_multi_ttl_with_redis_fallback',
            'environment': 'local_development'
        }

        # Add stats for each cache type
        for cache_name, cache in self.caches.items():
            stats[f'{cache_name}_cache_size'] = len(cache)
            stats[f'{cache_name}_cache_maxsize'] = cache.maxsize
            stats[f'{cache_name}_cache_ttl'] = self.cache_ttl.get(cache_name, 'unknown')

        # Add Redis stats if available
        if self.redis_client:
            try:
                info = self.redis_client.info('memory')
                stats['redis_memory_used'] = info.get('used_memory_human', 'N/A')
                stats['redis_keys'] = self.redis_client.dbsize()
            except Exception as e:
                stats['redis_error'] = str(e)

        return stats

# Global cache instance
cache_manager = CacheManager()

# Convenience functions for specific data types
def cache_routes(key: str, data: Any) -> bool:
    """Cache route data with appropriate TTL."""
    return cache_manager.set(key, data, 'routes')

def get_cached_routes(key: str) -> Optional[Any]:
    """Get cached route data."""
    return cache_manager.get(key, 'routes')

def cache_stops(key: str, data: Any) -> bool:
    """Cache stop data with appropriate TTL."""
    return cache_manager.set(key, data, 'stops')

def get_cached_stops(key: str) -> Optional[Any]:
    """Get cached stop data."""
    return cache_manager.get(key, 'stops')

def cache_eta(key: str, data: Any) -> bool:
    """Cache ETA data with short TTL."""
    return cache_manager.set(key, data, 'eta')

def get_cached_eta(key: str) -> Optional[Any]:
    """Get cached ETA data."""
    return cache_manager.get(key, 'eta')

def cache_routestops(key: str, data: Any) -> bool:
    """Cache route-stop data with medium TTL."""
    return cache_manager.set(key, data, 'routestops')

def get_cached_routestops(key: str) -> Optional[Any]:
    """Get cached route-stop data."""
    return cache_manager.get(key, 'routestops')

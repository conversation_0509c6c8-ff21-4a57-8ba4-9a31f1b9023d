#!/usr/bin/env python3
"""
Test script to verify all bus companies (KMB, CTB, GMB) are loading correctly.
"""

import requests
import json
import time

def test_routes_by_company(url):
    """Test and analyze routes by company."""
    print(f"\n🧪 Testing: {url}")
    
    try:
        start_time = time.time()
        response = requests.get(url, timeout=30)
        elapsed = time.time() - start_time
        
        print(f"⏱️  Response time: {elapsed:.2f}s")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if isinstance(data, list) and len(data) > 0:
                # Count routes by company
                companies = {}
                for route in data:
                    co = route.get('co', 'Unknown')
                    companies[co] = companies.get(co, 0) + 1
                
                print(f"✅ Total routes: {len(data)}")
                print("📋 Routes by company:")
                for company, count in companies.items():
                    print(f"   - {company}: {count} routes")
                
                # Show sample routes for each company
                print("\n📝 Sample routes:")
                for company in companies.keys():
                    sample_routes = [r for r in data if r.get('co') == company][:3]
                    for route in sample_routes:
                        route_code = route.get('route', 'N/A')
                        dest = route.get('dest_en', route.get('dest_tc', 'N/A'))
                        print(f"   - {company} {route_code}: {dest}")
                
                return companies
            else:
                print("❌ No route data received")
                return {}
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}...")
            return {}
            
    except requests.exceptions.Timeout:
        print("⏰ Timeout: Request took longer than 30 seconds")
        return {}
    except Exception as e:
        print(f"❌ Error: {e}")
        return {}

def test_warmup_endpoints(base_url):
    """Test warmup endpoints."""
    print(f"\n🔥 Testing Warmup Endpoints")
    print("=" * 40)
    
    # Test background warmup
    print("\n1. Testing background warmup...")
    try:
        response = requests.get(f"{base_url}/warmup_background", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Background warmup: {result.get('status')}")
        else:
            print(f"❌ Background warmup failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Background warmup error: {e}")
    
    # Wait a bit for background loading
    print("\n⏳ Waiting 10 seconds for background loading...")
    time.sleep(10)
    
    # Test full warmup
    print("\n2. Testing full warmup...")
    try:
        response = requests.get(f"{base_url}/warmup", timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Full warmup: {result.get('status')}")
            print(f"   - CTB routes: {result.get('ctb_routes_count', 0)}")
            print(f"   - GMB routes: {result.get('gmb_routes_count', 0)}")
            print(f"   - Time: {result.get('elapsed_seconds', 0)}s")
        else:
            print(f"❌ Full warmup failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Full warmup error: {e}")

def main():
    """Test all route loading functionality."""
    # Change this URL for your deployed Vercel app
    base_url = "https://your-app.vercel.app"  # Update this!
    # For local testing:
    # base_url = "http://localhost:5000"
    
    print("🚌 Testing All Bus Routes Loading")
    print("=" * 50)
    
    # Test 1: Main routes endpoint (should have KMB + CTB)
    print("\n1️⃣ Testing Main Routes (Before Warmup)")
    main_companies_before = test_routes_by_company(f"{base_url}/get_bus_routes")
    
    # Test 3: Warmup endpoints
    test_warmup_endpoints(base_url)
    
    # Test 4: Main routes endpoint after warmup
    print("\n3️⃣ Testing Main Routes (After Warmup)")
    main_companies_after = test_routes_by_company(f"{base_url}/get_bus_routes")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    print(f"\n📋 Main Routes (Before): {sum(main_companies_before.values())} total")
    for co, count in main_companies_before.items():
        print(f"   - {co}: {count}")
    
    print(f"\n🎯 Main Routes (After): {sum(main_companies_after.values())} total")
    for co, count in main_companies_after.items():
        print(f"   - {co}: {count}")
    
    # Check if required companies are present (GMB temporarily disabled)
    expected_companies = {'KMB', 'CTB'}  # GMB temporarily disabled
    final_companies = set(main_companies_after.keys())

    if expected_companies.issubset(final_companies):
        print("\n✅ SUCCESS: Required bus companies (KMB, CTB) are present!")
        print("ℹ️  Note: GMB routes are temporarily disabled for performance")
    else:
        missing = expected_companies - final_companies
        print(f"\n❌ FAILURE: Missing required companies: {missing}")
        print("💡 This is not acceptable - both KMB and CTB routes are required.")
    
    print(f"\n🎉 Test complete!")

if __name__ == "__main__":
    main()

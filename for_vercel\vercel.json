{"builds": [{"src": "app.py", "use": "@vercel/python", "config": {"functions": {"app.py": {"maxDuration": 30}}}}, {"src": "index.html", "use": "@vercel/static"}], "routes": [{"src": "/", "dest": "/index.html"}, {"src": "/get_(.*)", "dest": "/app.py"}, {"src": "/health", "dest": "/app.py"}, {"src": "/warmup", "dest": "/app.py"}, {"src": "/warmup_background", "dest": "/app.py"}, {"src": "/cache/(.*)", "dest": "/app.py"}, {"src": "/stats", "dest": "/app.py"}], "env": {"PYTHONPATH": "."}}
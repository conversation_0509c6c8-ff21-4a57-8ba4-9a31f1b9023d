#!/usr/bin/env python3
"""
Test script to verify the Vercel endpoints work correctly.
Run this locally to test before deploying.
"""

import requests
import json
import time

def test_endpoint(url, endpoint_name):
    """Test a single endpoint."""
    print(f"\n🧪 Testing {endpoint_name}...")
    print(f"URL: {url}")
    
    try:
        start_time = time.time()
        response = requests.get(url, timeout=30)
        elapsed = time.time() - start_time
        
        print(f"⏱️  Response time: {elapsed:.2f}s")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if isinstance(data, list):
                    print(f"✅ Success: Received {len(data)} items")
                    if len(data) > 0:
                        print(f"📝 Sample item: {json.dumps(data[0], indent=2, ensure_ascii=False)[:200]}...")
                elif isinstance(data, dict):
                    print(f"✅ Success: {json.dumps(data, indent=2, ensure_ascii=False)}")
                else:
                    print(f"✅ Success: {data}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON Error: {e}")
                print(f"📄 Response text: {response.text[:200]}...")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print("⏰ Timeout: Request took longer than 30 seconds")
    except requests.exceptions.RequestException as e:
        print(f"❌ Request Error: {e}")

def main():
    """Test all endpoints."""
    base_url = "http://localhost:5000"  # Change this for deployed version
    
    print("🚀 Testing Vercel Flask App Endpoints")
    print("=" * 50)
    
    # Test health endpoint first
    test_endpoint(f"{base_url}/health", "Health Check")
    
    # Test warmup endpoint
    test_endpoint(f"{base_url}/warmup", "Warmup")
    
    # Test main routes endpoint
    test_endpoint(f"{base_url}/get_bus_routes", "Bus Routes")
    
    # Test stats endpoint
    test_endpoint(f"{base_url}/stats", "Statistics")
    
    print("\n" + "=" * 50)
    print("🏁 Testing complete!")

if __name__ == "__main__":
    main()
